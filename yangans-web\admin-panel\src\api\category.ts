import { request } from './client'
import type { ApiResponse } from '@/types'
import type { CategoryVO, PageResult } from './article'

export interface CategoryDTO {
  id?: number
  name: string
  description?: string
  sort?: number
}

// 分类API接口
export const categoryApi = {
  // 获取所有分类列表（不分页）
  getAllCategories(): Promise<ApiResponse<CategoryVO[]>> {
    return request.get('/admin/categories')
  },

  // 根据ID查询分类详情
  getCategoryById(id: number): Promise<ApiResponse<CategoryVO>> {
    return request.get(`/admin/categories/${id}`)
  },

  // 创建分类
  createCategory(data: CategoryDTO): Promise<ApiResponse<CategoryVO>> {
    return request.post('/admin/categories', data)
  },

  // 更新分类
  updateCategory(id: number, data: CategoryDTO): Promise<ApiResponse<CategoryVO>> {
    return request.put(`/admin/categories/${id}`, data)
  },

  // 删除分类
  deleteCategory(id: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/categories/${id}`)
  }
}

# YangAns博客系统 - 前后端联调指南

## 🎯 联调目标

完成文章管理系统的前后端联调，确保以下功能正常工作：
- 文章的增删改查操作
- Markdown编辑器的保存和预览
- 文章发布状态切换
- 分类标签的关联和管理
- 分页和搜索功能

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）
```bash
# 双击运行启动脚本
start-dev.bat
```

### 方式二：手动启动
```bash
# 1. 启动后端服务
cd yangans-admin
mvn spring-boot:run

# 2. 启动管理后台
cd yangans-web/admin-panel
npm run dev

# 3. 启动主站（可选）
cd yangans-web/main-site
npm run dev
```

## 📋 环境要求

### 必需环境
- **Java**: JDK 8或更高版本
- **Node.js**: 16或更高版本
- **MySQL**: 8.0或更高版本
- **Maven**: 3.6或更高版本

### 可选环境
- **Redis**: 6.0或更高版本（缓存功能）

## 🔧 配置检查

### 1. 数据库配置
确保MySQL服务正在运行，并创建数据库：
```sql
CREATE DATABASE yangans_blog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 后端配置
检查 `yangans-admin/src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: ****************************************
    username: root
    password: 123456
```

### 3. 前端配置
检查 `yangans-web/admin-panel/.env`：
```env
VITE_API_BASE_URL=http://localhost:8080/api
VITE_SKIP_AUTH=true
```

## 🧪 联调测试步骤

### 第一步：验证服务启动
1. **后端服务**: 访问 http://localhost:8080/api/swagger-ui/
2. **管理后台**: 访问 http://localhost:3002
3. **主站**: 访问 http://localhost:3000

### 第二步：API连接测试
1. 在管理后台中访问 `/test/api` 页面（需要手动添加路由）
2. 点击各个API测试按钮
3. 检查是否能正常获取数据

### 第三步：文章管理功能测试
1. **文章列表页面**:
   - 访问 `/articles` 页面
   - 检查是否能正常加载文章列表
   - 测试搜索和筛选功能
   - 测试分页功能

2. **文章编辑页面**:
   - 访问 `/articles/new` 创建新文章
   - 测试Markdown编辑器
   - 测试保存草稿功能
   - 测试发布功能

3. **文章操作功能**:
   - 测试编辑已有文章
   - 测试删除文章
   - 测试批量操作
   - 测试发布/撤回功能
   - 测试置顶/取消置顶功能

### 第四步：数据格式验证
1. 检查前后端数据格式是否一致
2. 验证分页参数和响应格式
3. 确认状态值映射正确
4. 检查时间格式处理

## 🐛 常见问题排查

### 1. 后端服务无法启动
**问题**: 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :8080
# 杀死占用进程
taskkill /PID <进程ID> /F
```

**问题**: 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库连接信息
- 确认数据库是否存在

### 2. 前端无法连接后端
**问题**: CORS跨域错误
- 检查后端CORS配置
- 确认前端API地址配置正确

**问题**: 404错误
- 检查API路径是否正确
- 确认后端Controller路径映射

### 3. 数据格式不匹配
**问题**: 字段名称不一致
- 检查前后端字段映射
- 使用数据转换函数

**问题**: 类型转换错误
- 检查ID类型（string vs number）
- 验证日期格式处理

## 📊 API接口清单

### 文章管理API
- `GET /api/admin/articles` - 分页查询文章列表
- `GET /api/admin/articles/{id}` - 查询文章详情
- `POST /api/admin/articles` - 创建文章
- `PUT /api/admin/articles/{id}` - 更新文章
- `DELETE /api/admin/articles/{id}` - 删除文章
- `DELETE /api/admin/articles/batch` - 批量删除文章
- `POST /api/admin/articles/{id}/publish` - 发布文章
- `POST /api/admin/articles/{id}/unpublish` - 撤回文章
- `POST /api/admin/articles/{id}/top` - 置顶文章
- `POST /api/admin/articles/{id}/untop` - 取消置顶

### 分类管理API
- `GET /api/admin/categories` - 获取所有分类
- `POST /api/admin/categories` - 创建分类
- `PUT /api/admin/categories/{id}` - 更新分类
- `DELETE /api/admin/categories/{id}` - 删除分类

### 标签管理API
- `GET /api/admin/tags` - 分页查询标签
- `GET /api/admin/tags/all` - 获取所有标签
- `POST /api/admin/tags` - 创建标签
- `PUT /api/admin/tags/{id}` - 更新标签
- `DELETE /api/admin/tags/{id}` - 删除标签

## 🔍 调试技巧

### 1. 浏览器开发者工具
- 检查Network标签页的API请求
- 查看Console标签页的错误信息
- 使用Vue DevTools调试组件状态

### 2. 后端日志
- 查看控制台输出的SQL语句
- 检查Spring Security的认证日志
- 监控异常堆栈信息

### 3. 数据库查询
```sql
-- 检查文章数据
SELECT * FROM articles ORDER BY created_at DESC LIMIT 10;

-- 检查分类数据
SELECT * FROM categories ORDER BY sort ASC;

-- 检查标签数据
SELECT * FROM tags ORDER BY use_count DESC LIMIT 10;
```

## ✅ 验收标准

### 功能完整性
- [ ] 文章列表正常显示
- [ ] 文章创建功能正常
- [ ] 文章编辑功能正常
- [ ] 文章删除功能正常
- [ ] 文章发布/撤回功能正常
- [ ] 文章置顶功能正常
- [ ] 分页功能正常
- [ ] 搜索筛选功能正常
- [ ] 分类标签管理正常

### 用户体验
- [ ] 页面加载速度合理
- [ ] 操作响应及时
- [ ] 错误提示友好
- [ ] 界面交互流畅

### 数据一致性
- [ ] 前后端数据格式一致
- [ ] 状态值映射正确
- [ ] 时间格式统一
- [ ] 分页参数正确

## 📞 技术支持

如果在联调过程中遇到问题，请：
1. 检查控制台错误信息
2. 查看网络请求详情
3. 确认配置文件设置
4. 参考本文档的排查步骤

联调成功后，即可进行下一步的功能开发和优化工作。

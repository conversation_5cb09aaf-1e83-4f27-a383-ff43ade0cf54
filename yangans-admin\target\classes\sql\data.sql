-- YangAns个人网站初始化数据
USE `yangans_blog`;

-- 1. 初始化管理员用户
-- 密码：admin123 (BCrypt加密)
INSERT INTO `users` (`id`, `username`, `password`, `email`, `nickname`, `role`, `status`) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctqp33Lsqp7.Hk8ZfJON.LjCaG', '<EMAIL>', 'YangAns', 'ADMIN', 1);

-- 2. 初始化分类数据
INSERT INTO `categories` (`id`, `name`, `description`, `sort`) VALUES
(1, '前端开发', '前端技术相关文章', 1),
(2, '后端开发', '后端技术相关文章', 2),
(3, '数据库', '数据库相关技术文章', 3),
(4, '算法与数据结构', '算法学习和数据结构相关', 4),
(5, '工具与效率', '开发工具和效率提升相关', 5),
(6, '生活随笔', '个人生活感悟和随笔', 6),
(7, '技术分享', '技术经验分享', 7),
(8, '项目实战', '项目开发实战经验', 8);

-- 3. 初始化标签数据
INSERT INTO `tags` (`id`, `name`, `color`) VALUES
(1, 'Vue3', '#4FC08D'),
(2, 'React', '#61DAFB'),
(3, 'JavaScript', '#F7DF1E'),
(4, 'TypeScript', '#3178C6'),
(5, 'Node.js', '#339933'),
(6, 'Spring Boot', '#6DB33F'),
(7, 'Java', '#ED8B00'),
(8, 'MySQL', '#4479A1'),
(9, 'Redis', '#DC382D'),
(10, 'Docker', '#2496ED'),
(11, 'Git', '#F05032'),
(12, 'Linux', '#FCC624'),
(13, 'Nginx', '#009639'),
(14, 'Maven', '#C71A36'),
(15, 'MyBatis', '#271E1E'),
(16, 'HTML', '#E34F26'),
(17, 'CSS', '#1572B6'),
(18, 'Sass', '#CC6699'),
(19, 'Webpack', '#8DD6F9'),
(20, 'Vite', '#646CFF'),
(21, '算法', '#FF6B6B'),
(22, '数据结构', '#4ECDC4'),
(23, '设计模式', '#45B7D1'),
(24, '微服务', '#96CEB4'),
(25, '性能优化', '#FFEAA7');

-- 4. 初始化示例文章
INSERT INTO `articles` (`id`, `title`, `content`, `summary`, `category_id`, `author_id`, `status`, `view_count`, `like_count`, `is_top`, `published_at`) VALUES
(1, 'Vue3 Composition API 深度解析', 
'# Vue3 Composition API 深度解析

## 简介

Vue3 引入了全新的 **Composition API**，为开发者提供了更灵活的组件逻辑组织方式。

## 核心特性

### 1. setup() 函数
```javascript
import { ref, reactive } from ''vue''

export default {
  setup() {
    const count = ref(0)
    const state = reactive({ name: ''Vue3'' })
    
    return { count, state }
  }
}
```

### 2. 响应式系统
- **ref()**: 用于基本类型数据
- **reactive()**: 用于对象类型数据  
- **computed()**: 计算属性
- **watch()**: 监听器

## 最佳实践

1. 合理使用 ref 和 reactive
2. 避免解构响应式对象
3. 使用 toRefs 保持响应性

## 总结

Composition API 让我们能够更好地组织和复用组件逻辑，是Vue3的重要特性。',
'详细介绍Vue3 Composition API的使用方法和最佳实践', 1, 1, 'PUBLISHED', 1250, 89, 1, NOW()),

(2, 'Spring Boot 微服务架构实践',
'# Spring Boot 微服务架构实践

## 微服务架构概述

微服务架构是一种将单一应用程序开发为一组小型服务的方法。

## Spring Boot 优势

### 1. 自动配置
Spring Boot 提供了大量的自动配置，减少了样板代码。

### 2. 内嵌服务器
无需部署到外部容器，直接运行jar包即可。

### 3. 生产就绪
提供了健康检查、指标监控等生产环境必需的功能。

## 实践案例

本文将通过一个实际项目来演示如何使用Spring Boot构建微服务。

```java
@SpringBootApplication
@EnableEurekaClient
public class UserServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}
```

## 总结

Spring Boot为微服务开发提供了强大的支持，是Java开发者的首选框架。',
'通过实际案例介绍Spring Boot微服务架构的设计和实现', 2, 1, 'PUBLISHED', 890, 67, 0, NOW()),

(3, 'MySQL性能优化实战指南',
'# MySQL性能优化实战指南

## 性能优化的重要性

数据库性能直接影响应用的响应速度和用户体验。

## 优化策略

### 1. 索引优化
- 合理创建索引
- 避免过多索引
- 使用复合索引

### 2. 查询优化
- 避免SELECT *
- 使用LIMIT分页
- 优化WHERE条件

### 3. 配置优化
- 调整缓冲池大小
- 优化连接数配置
- 设置合适的超时时间

## 监控工具

推荐使用以下工具进行性能监控：
- MySQL Workbench
- Percona Toolkit
- pt-query-digest

## 总结

MySQL性能优化是一个持续的过程，需要根据实际情况不断调整。',
'MySQL数据库性能优化的实战经验和技巧分享', 3, 1, 'PUBLISHED', 756, 45, 0, NOW());

-- 5. 初始化文章标签关联
INSERT INTO `article_tags` (`article_id`, `tag_id`) VALUES
(1, 1), (1, 3), (1, 4),  -- Vue3文章关联Vue3、JavaScript、TypeScript标签
(2, 6), (2, 7), (2, 24), -- Spring Boot文章关联Spring Boot、Java、微服务标签
(3, 8), (3, 25);         -- MySQL文章关联MySQL、性能优化标签

-- 6. 更新分类文章数量
UPDATE `categories` SET `article_count` = 1 WHERE `id` IN (1, 2, 3);

-- 7. 更新标签使用次数
UPDATE `tags` SET `use_count` = 1 WHERE `id` IN (1, 3, 4, 6, 7, 8, 24, 25);

-- 8. 初始化系统配置
INSERT INTO `system_configs` (`config_key`, `config_value`, `description`, `type`) VALUES
('site.title', 'YangAns个人博客', '网站标题', 'STRING'),
('site.description', '一个专注于技术分享的个人博客', '网站描述', 'STRING'),
('site.keywords', 'YangAns,博客,技术,前端,后端,Vue,Spring Boot', '网站关键词', 'STRING'),
('site.author', 'YangAns', '网站作者', 'STRING'),
('site.email', '<EMAIL>', '联系邮箱', 'STRING'),
('site.icp', '', 'ICP备案号', 'STRING'),
('comment.enable', 'true', '是否开启评论', 'BOOLEAN'),
('comment.audit', 'true', '评论是否需要审核', 'BOOLEAN'),
('message.enable', 'true', '是否开启留言', 'BOOLEAN'),
('message.audit', 'true', '留言是否需要审核', 'BOOLEAN'),
('upload.max_size', '10485760', '文件上传最大大小（字节）', 'NUMBER'),
('upload.allowed_types', 'jpg,jpeg,png,gif,webp,pdf,doc,docx', '允许上传的文件类型', 'STRING');

-- 9. 初始化示例留言
INSERT INTO `messages` (`nickname`, `email`, `content`, `status`, `ip`) VALUES
('张三', '<EMAIL>', '博客内容很棒，学到了很多！', 'APPROVED', '*************'),
('李四', '<EMAIL>', '期待更多技术分享文章', 'APPROVED', '*************'),
('王五', '<EMAIL>', '网站设计很简洁，用户体验不错', 'PENDING', '*************');

-- 10. 初始化示例生活记录
INSERT INTO `life_records` (`title`, `content`, `category`, `mood`, `weather`) VALUES
('项目上线成功', '经过一个月的努力，个人博客项目终于成功上线了！感谢所有帮助过我的朋友们。', 'WORK', '开心', '晴天'),
('学习Vue3新特性', '今天深入学习了Vue3的Composition API，发现确实比Options API更加灵活。', 'STUDY', '充实', '多云'),
('周末户外徒步', '和朋友们一起去爬山，呼吸新鲜空气，放松心情。', 'LIFE', '愉快', '晴天');

-- 11. 初始化示例番剧数据
INSERT INTO `animes` (`name`, `name_cn`, `summary`, `total_episodes`, `air_date`, `status`, `rating`, `current_episode`) VALUES
('鬼灭之刃', '鬼灭之刃', '以大正时代为背景，讲述了主人公炭治郎为了治好变成鬼的妹妹，以及为了讨伐杀害家人的鬼，而加入鬼杀队的故事。', 26, '2019-04-06', 'WATCHED', 9.2, 26),
('进击的巨人', '进击的巨人', '人类与巨人的生存之战，充满了悬疑和反转的史诗级作品。', 75, '2013-04-07', 'WATCHED', 9.5, 75),
('咒术回战', '咒术回战', '现代都市背景下的超自然战斗番，画面精美，战斗场面燃爆。', 24, '2020-10-03', 'WATCHING', 8.8, 18);

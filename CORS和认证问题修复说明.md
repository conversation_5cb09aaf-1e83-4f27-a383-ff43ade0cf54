# CORS和认证问题修复说明

## 🐛 问题描述

在进行前后端联调时遇到了以下错误：

1. **CORS跨域错误**：
   ```
   Access to XMLHttpRequest at 'http://localhost:8080/api/admin/categories' 
   from origin 'http://localhost:3002' has been blocked by CORS policy: 
   No 'Access-Control-Allow-Origin' header is present on the requested resource.
   ```

2. **认证错误**：
   ```
   GET http://localhost:8080/api/admin/categories net::ERR_FAILED 401 (Unauthorized)
   ```

## 🔍 问题分析

### 1. CORS配置问题
- 后端虽然配置了CORS，但使用的是旧版Spring Security API
- CORS配置没有正确应用到SecurityFilterChain中

### 2. 认证Token问题
- 前端在开发模式下设置了模拟用户，但axios拦截器仍从localStorage获取token
- 开发模式下localStorage中没有有效的token

### 3. Spring Security版本问题
- 使用了已弃用的`WebSecurityConfigurerAdapter`
- 需要升级到新的`SecurityFilterChain` API

## 🛠️ 修复方案

### 1. 前端修复 - axios拦截器

**文件**: `yangans-web/admin-panel/src/api/client.ts`

**修复内容**:
```typescript
// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 🚧 开发模式：检查是否跳过认证
    const skipAuth = import.meta.env.VITE_SKIP_AUTH === 'true'
    const isDevelopment = import.meta.env.DEV || import.meta.env.VITE_APP_ENV === 'development'
    
    if (skipAuth && isDevelopment) {
      // 开发模式下使用模拟token
      config.headers.Authorization = 'Bearer dev-mock-token'
      console.log('🚧 开发模式：使用模拟token')
    } else {
      // 生产模式或需要认证时，从localStorage获取token
      const token = localStorage.getItem('admin-token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    }
    
    // 其他配置...
    return config
  },
  // 错误处理...
)
```

**修复效果**:
- ✅ 开发模式下自动使用模拟token
- ✅ 生产模式下正常使用真实token
- ✅ 避免了认证相关的错误

### 2. 后端修复 - Spring Security配置

**文件**: `yangans-admin/src/main/java/com/yangans/admin/component/security/SecurityConfig.java`

**修复内容**:
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final CorsConfigurationSource corsConfigurationSource;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            // 启用CORS
            .cors(cors -> cors.configurationSource(corsConfigurationSource))
            
            // 禁用CSRF
            .csrf(csrf -> csrf.disable())

            // 配置会话管理为无状态
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))

            // 配置异常处理
            .exceptionHandling(exception -> exception
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
            )

            // 配置请求授权
            .authorizeRequests(auth -> auth
                // 主站公开API - 允许匿名访问
                .antMatchers("/api/public/**").permitAll()
                // 管理后台登录接口 - 允许匿名访问
                .antMatchers("/api/admin/auth/login").permitAll()
                // Swagger和监控接口 - 允许匿名访问
                .antMatchers("/swagger-ui/**", "/v3/api-docs/**", "/actuator/**", "/error").permitAll()
                // 🚧 开发模式：临时允许所有管理后台接口（用于测试）
                .antMatchers("/api/admin/**").permitAll()
                // 其他请求 - 需要认证
                .anyRequest().authenticated()
            )

            // 添加JWT过滤器
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)

            // 配置安全头
            .headers(headers -> headers
                .frameOptions().deny()
                .contentTypeOptions().and()
                .httpStrictTransportSecurity(hstsConfig -> hstsConfig
                    .maxAgeInSeconds(31536000)
                    .includeSubDomains(true)
                )
            )
            .build();
    }
}
```

**修复要点**:
- ✅ 升级到新的Spring Security API
- ✅ 正确应用CORS配置
- ✅ 临时允许所有管理后台接口（开发模式）
- ✅ 保持JWT认证机制

### 3. CORS配置验证

**现有配置** (`application.yml`):
```yaml
yangans:
  cors:
    allowed-origins:
      - http://localhost:3000
      - http://localhost:3001
      - http://localhost:3002  # ✅ 已包含管理后台端口
      - http://localhost:3003
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: '*'
    allow-credentials: true
    max-age: 3600
```

## 🧪 测试验证

### 1. 使用测试脚本
```bash
# 运行API连接测试
test-api-connection.bat
```

### 2. 手动测试步骤

1. **启动后端服务**:
   ```bash
   cd yangans-admin
   mvn spring-boot:run
   ```

2. **启动前端服务**:
   ```bash
   cd yangans-web/admin-panel
   npm run dev
   ```

3. **访问管理后台**:
   - 打开浏览器访问 `http://localhost:3002`
   - 进入文章管理页面
   - 检查浏览器控制台是否有错误

4. **验证API调用**:
   - 检查Network标签页的API请求
   - 确认响应状态码为200
   - 确认数据正常返回

### 3. 预期结果

✅ **成功指标**:
- 无CORS跨域错误
- API请求返回200状态码
- 文章列表正常显示
- 分类标签正常加载
- 浏览器控制台无错误

❌ **如果仍有问题**:
- 检查后端服务是否正常启动
- 确认端口3002和8080没有被占用
- 查看后端控制台日志
- 检查防火墙设置

## 🔧 开发模式说明

### 临时认证绕过
为了方便开发测试，当前配置临时允许所有管理后台接口无需认证：

```java
// 🚧 开发模式：临时允许所有管理后台接口（用于测试）
.antMatchers("/api/admin/**").permitAll()
```

### 生产环境恢复
在生产环境部署前，需要恢复正常的认证要求：

```java
// 🔒 生产模式：管理后台接口需要认证
.antMatchers("/api/admin/**").authenticated()
```

## 📋 检查清单

- [x] 前端axios拦截器支持开发模式
- [x] 后端Spring Security升级到新API
- [x] CORS配置正确应用
- [x] 临时认证绕过（开发模式）
- [x] 测试脚本创建
- [ ] 前后端联调测试
- [ ] 功能完整性验证
- [ ] 生产环境认证恢复

## 🚀 下一步

1. **运行测试**: 使用提供的测试脚本验证修复效果
2. **功能测试**: 完整测试文章管理的所有功能
3. **性能优化**: 根据测试结果进行性能调优
4. **认证恢复**: 开发完成后恢复正常的认证机制

修复完成后，前后端应该能够正常通信，可以继续进行功能开发和测试。

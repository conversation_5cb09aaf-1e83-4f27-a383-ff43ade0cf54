# 生产环境配置
spring:
  # 数据源配置
  datasource:
    url: *****************************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      connection-timeout: 60000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}

# 日志配置
logging:
  level:
    com.yangans.admin: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.mybatis: WARN
    com.baomidou.mybatisplus: WARN
    root: WARN
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: /var/log/yangans/yangans-admin.log
    max-size: 50MB
    max-history: 30

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

# 自定义配置
yangans:
  # 生产环境JWT配置
  jwt:
    secret: ${JWT_SECRET:yangans-blog-jwt-secret-key-production-2024}
    expiration: 43200000  # 12小时
  
  # 生产环境文件上传路径
  upload:
    path: /var/uploads/yangans/
  
  # 生产环境跨域配置
  cors:
    allowed-origins: 
      - https://yangans.com
      - https://admin.yangans.com
      - https://www.yangans.com

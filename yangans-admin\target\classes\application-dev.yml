# 开发环境配置
spring:
  # 数据源配置
  datasource:
    url: ***********************************************************************************************************************************************************************************
    username: root
    password: 123456
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

# 日志配置
logging:
  level:
    com.yangans.admin: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 自定义配置
yangans:
  # 开发环境文件上传路径
  upload:
    path: D:/uploads/yangans/
  
  # 开发环境跨域配置
  cors:
    allowed-origins: 
      - http://localhost:3000
      - http://localhost:3001
      - http://localhost:3002
      - http://localhost:3003
      - http://127.0.0.1:3000
      - http://127.0.0.1:3001
      - http://127.0.0.1:3002
      - http://127.0.0.1:3003

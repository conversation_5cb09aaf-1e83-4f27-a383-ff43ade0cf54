<template>
  <div class="api-test">
    <el-card>
      <template #header>
        <h3>API连接测试</h3>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <!-- 文章API测试 -->
        <el-card shadow="never">
          <template #header>
            <h4>文章API测试</h4>
          </template>
          
          <el-space wrap>
            <el-button @click="testGetArticles" :loading="loading.articles">
              获取文章列表
            </el-button>
            <el-button @click="testGetCategories" :loading="loading.categories">
              获取分类列表
            </el-button>
            <el-button @click="testGetTags" :loading="loading.tags">
              获取标签列表
            </el-button>
          </el-space>
          
          <div v-if="results.articles" class="result-section">
            <h5>文章列表结果：</h5>
            <pre>{{ JSON.stringify(results.articles, null, 2) }}</pre>
          </div>
          
          <div v-if="results.categories" class="result-section">
            <h5>分类列表结果：</h5>
            <pre>{{ JSON.stringify(results.categories, null, 2) }}</pre>
          </div>
          
          <div v-if="results.tags" class="result-section">
            <h5>标签列表结果：</h5>
            <pre>{{ JSON.stringify(results.tags, null, 2) }}</pre>
          </div>
        </el-card>
        
        <!-- 错误信息 -->
        <el-card v-if="errors.length > 0" shadow="never">
          <template #header>
            <h4>错误信息</h4>
          </template>
          
          <el-alert
            v-for="(error, index) in errors"
            :key="index"
            :title="error.title"
            :description="error.message"
            type="error"
            :closable="false"
            style="margin-bottom: 10px"
          />
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { articleApi } from '@/api/article'
import { categoryApi } from '@/api/category'
import { tagApi } from '@/api/tag'

// 响应式数据
const loading = reactive({
  articles: false,
  categories: false,
  tags: false
})

const results = reactive({
  articles: null as any,
  categories: null as any,
  tags: null as any
})

const errors = ref<Array<{ title: string; message: string }>>([])

// 清除错误
const clearErrors = () => {
  errors.value = []
}

// 添加错误
const addError = (title: string, message: string) => {
  errors.value.push({ title, message })
}

// 测试获取文章列表
const testGetArticles = async () => {
  clearErrors()
  loading.articles = true
  
  try {
    const response = await articleApi.getArticles({
      current: 1,
      size: 5
    })
    
    results.articles = response
    
    if (response.code === 200) {
      ElMessage.success('文章API测试成功')
    } else {
      addError('文章API错误', response.message || '未知错误')
    }
  } catch (error: any) {
    console.error('文章API测试失败:', error)
    addError('文章API请求失败', error.message || '网络错误')
  } finally {
    loading.articles = false
  }
}

// 测试获取分类列表
const testGetCategories = async () => {
  clearErrors()
  loading.categories = true
  
  try {
    const response = await categoryApi.getAllCategories()
    
    results.categories = response
    
    if (response.code === 200) {
      ElMessage.success('分类API测试成功')
    } else {
      addError('分类API错误', response.message || '未知错误')
    }
  } catch (error: any) {
    console.error('分类API测试失败:', error)
    addError('分类API请求失败', error.message || '网络错误')
  } finally {
    loading.categories = false
  }
}

// 测试获取标签列表
const testGetTags = async () => {
  clearErrors()
  loading.tags = true
  
  try {
    const response = await tagApi.getAllTags()
    
    results.tags = response
    
    if (response.code === 200) {
      ElMessage.success('标签API测试成功')
    } else {
      addError('标签API错误', response.message || '未知错误')
    }
  } catch (error: any) {
    console.error('标签API测试失败:', error)
    addError('标签API请求失败', error.message || '网络错误')
  } finally {
    loading.tags = false
  }
}
</script>

<style lang="scss" scoped>
.api-test {
  padding: 20px;
  
  .result-section {
    margin-top: 20px;
    
    h5 {
      margin-bottom: 10px;
      color: #409eff;
    }
    
    pre {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      max-height: 300px;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}
</style>

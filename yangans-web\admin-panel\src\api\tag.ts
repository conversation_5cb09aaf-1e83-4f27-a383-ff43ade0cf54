import { request } from './client'
import type { ApiResponse } from '@/types'
import type { TagVO, PageResult } from './article'

export interface TagDTO {
  id?: number
  name: string
  color?: string
}

export interface TagQueryParams {
  current?: number
  size?: number
  name?: string
}

// 标签API接口
export const tagApi = {
  // 分页查询标签列表
  getTags(params: TagQueryParams): Promise<ApiResponse<PageResult<TagVO>>> {
    return request.get('/admin/tags', { params })
  },

  // 获取所有标签列表（不分页）
  getAllTags(): Promise<ApiResponse<TagVO[]>> {
    return request.get('/admin/tags/all')
  },

  // 根据ID查询标签详情
  getTagById(id: number): Promise<ApiResponse<TagVO>> {
    return request.get(`/admin/tags/${id}`)
  },

  // 创建标签
  createTag(data: TagDTO): Promise<ApiResponse<TagVO>> {
    return request.post('/admin/tags', data)
  },

  // 更新标签
  updateTag(id: number, data: TagDTO): Promise<ApiResponse<TagVO>> {
    return request.put(`/admin/tags/${id}`, data)
  },

  // 删除标签
  deleteTag(id: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/tags/${id}`)
  },

  // 批量删除标签
  batchDeleteTags(ids: number[]): Promise<ApiResponse<void>> {
    return request.delete('/admin/tags/batch', { data: ids })
  },

  // 获取热门标签
  getHotTags(limit: number = 10): Promise<ApiResponse<TagVO[]>> {
    return request.get('/admin/tags/hot', { params: { limit } })
  },

  // 批量创建标签
  batchCreateTags(names: string[]): Promise<ApiResponse<TagVO[]>> {
    return request.post('/admin/tags/batch', { names })
  }
}

import { request } from './client'
import type { ApiResponse } from '@/types'

// 文章相关类型定义（与后端对应）
export interface ArticleVO {
  id: number
  title: string
  content: string
  summary?: string
  cover?: string
  status: string // DRAFT, PUBLISHED
  categoryId?: number
  category?: CategoryVO
  authorId: number
  author?: UserVO
  viewCount: number
  likeCount: number
  commentCount: number
  isTop: boolean
  publishedAt?: string
  createdAt: string
  updatedAt: string
  tags?: TagVO[]
}

export interface ArticleDTO {
  id?: number
  title: string
  content: string
  summary?: string
  cover?: string
  status?: string
  categoryId?: number
  authorId: number
  isTop?: boolean
  publishedAt?: string
  tagIds?: number[]
}

export interface ArticleQueryDTO {
  current?: number
  size?: number
  title?: string
  status?: string
  categoryId?: number
  tagId?: number
  authorId?: number
  isTop?: boolean
  keyword?: string
  orderBy?: string
  orderDirection?: string
}

export interface PageResult<T> {
  records: T[]
  current: number
  size: number
  total: number
  pages: number
}

export interface CategoryVO {
  id: number
  name: string
  description?: string
  sort: number
  articleCount: number
  createdAt: string
  updatedAt: string
}

export interface TagVO {
  id: number
  name: string
  color?: string
  useCount: number
  createdAt: string
  updatedAt: string
}

export interface UserVO {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  role: string
  status: number
  lastLoginTime?: string
  lastLoginIp?: string
  createdAt: string
  updatedAt: string
}

// 文章API接口
export const articleApi = {
  // 分页查询文章列表
  getArticles(params: ArticleQueryDTO): Promise<ApiResponse<PageResult<ArticleVO>>> {
    return request.get('/admin/articles', { params })
  },

  // 根据ID查询文章详情
  getArticleById(id: number): Promise<ApiResponse<ArticleVO>> {
    return request.get(`/admin/articles/${id}`)
  },

  // 创建文章
  createArticle(data: ArticleDTO): Promise<ApiResponse<ArticleVO>> {
    return request.post('/admin/articles', data)
  },

  // 更新文章
  updateArticle(id: number, data: ArticleDTO): Promise<ApiResponse<ArticleVO>> {
    return request.put(`/admin/articles/${id}`, data)
  },

  // 删除文章
  deleteArticle(id: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/articles/${id}`)
  },

  // 批量删除文章
  batchDeleteArticles(ids: number[]): Promise<ApiResponse<void>> {
    return request.delete('/admin/articles/batch', { data: ids })
  },

  // 发布文章
  publishArticle(id: number): Promise<ApiResponse<void>> {
    return request.post(`/admin/articles/${id}/publish`)
  },

  // 撤回文章
  unpublishArticle(id: number): Promise<ApiResponse<void>> {
    return request.post(`/admin/articles/${id}/unpublish`)
  },

  // 置顶文章
  topArticle(id: number): Promise<ApiResponse<void>> {
    return request.post(`/admin/articles/${id}/top`)
  },

  // 取消置顶
  untopArticle(id: number): Promise<ApiResponse<void>> {
    return request.post(`/admin/articles/${id}/untop`)
  },

  // 批量发布文章
  batchPublishArticles(ids: number[]): Promise<ApiResponse<void>> {
    return request.post('/admin/articles/batch/publish', { data: ids })
  },

  // 获取文章统计信息
  getArticleStatistics(): Promise<ApiResponse<Record<string, any>>> {
    return request.get('/admin/articles/statistics')
  }
}

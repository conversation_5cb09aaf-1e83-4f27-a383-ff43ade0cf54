<template>
  <div class="article-edit">
    <div class="page-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="goBack" text>
          返回列表
        </el-button>
        <h1 class="page-title">{{ isEdit ? '编辑文章' : '新建文章' }}</h1>
      </div>
      <div class="header-right">
        <div class="save-status" v-if="lastSaveTime">
          <el-text size="small" type="info">
            <el-icon><Clock /></el-icon>
            上次保存: {{ formatSaveTime(lastSaveTime) }}
          </el-text>
        </div>
        <el-button @click="saveDraft" :loading="saving">
          <el-icon><DocumentCopy /></el-icon>
          保存草稿
        </el-button>
        <el-button type="primary" @click="publish" :loading="publishing">
          <el-icon><Promotion /></el-icon>
          {{ form.status === 'published' ? '更新发布' : '发布文章' }}
        </el-button>
      </div>
    </div>

    <div class="edit-container">
      <div class="edit-main">
        <!-- 基本信息 -->
        <el-card class="info-card" shadow="never">
          <el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
            <el-form-item label="文章标题" prop="title">
              <el-input
                v-model="form.title"
                placeholder="请输入文章标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="文章摘要">
              <el-input
                v-model="form.summary"
                type="textarea"
                placeholder="请输入文章摘要（可选）"
                :rows="3"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- Markdown编辑器 -->
        <el-card class="editor-card" shadow="never">
          <template #header>
            <div class="editor-header">
              <span>文章内容</span>
              <div class="editor-tips">
                <el-tooltip content="支持Markdown语法，实时预览" placement="top">
                  <el-tag size="small" type="info">Markdown</el-tag>
                </el-tooltip>
                <el-tooltip content="Ctrl+S 保存草稿，Ctrl+Shift+P 发布" placement="top">
                  <el-tag size="small" type="success">快捷键</el-tag>
                </el-tooltip>
              </div>
            </div>
          </template>

          <div class="editor-container">
            <div ref="editorRef" class="vditor-editor"></div>
          </div>
        </el-card>
      </div>

      <div class="edit-sidebar">
        <!-- 发布设置 -->
        <el-card class="sidebar-card" shadow="never">
          <template #header>
            <span>发布设置</span>
          </template>
          
          <el-form :model="form" label-width="60px" size="small">
            <el-form-item label="状态">
              <el-select v-model="form.status" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="分类">
              <el-select v-model="form.categoryId" placeholder="选择分类" style="width: 100%">
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="标签">
              <el-select
                v-model="form.tagIds"
                multiple
                filterable
                allow-create
                placeholder="选择或创建标签"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in tags"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="置顶">
              <el-switch v-model="form.isTop" />
            </el-form-item>
            
            <el-form-item label="发布时间" v-if="form.status === 'published'">
              <el-date-picker
                v-model="form.publishedAt"
                type="datetime"
                placeholder="选择发布时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DDTHH:mm:ss.SSSZ"
              />
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 封面图片 -->
        <el-card class="sidebar-card" shadow="never">
          <template #header>
            <span>封面图片</span>
          </template>
          
          <div class="cover-upload">
            <el-upload
              class="cover-uploader"
              :show-file-list="false"
              :before-upload="beforeCoverUpload"
              :on-success="handleCoverSuccess"
              action="/api/upload"
            >
              <img v-if="form.cover" :src="form.cover" class="cover-image" />
              <el-icon v-else class="cover-uploader-icon"><Plus /></el-icon>
            </el-upload>
            
            <div class="cover-actions" v-if="form.cover">
              <el-button size="small" @click="form.cover = ''">
                移除封面
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Clock, DocumentCopy, Promotion } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import type { ArticleForm, Category, Tag } from '@/types'
import { articleApi, type ArticleDTO } from '@/api/article'
import { categoryApi } from '@/api/category'
import { tagApi } from '@/api/tag'
import { transformArticleVOToArticle, transformArticleToArticleDTO, transformCategoryVOToCategory, transformTagVOToTag } from '@/utils/dataTransform'
import { useAuthStore } from '@/stores/auth'
import Vditor from 'vditor'
import 'vditor/dist/index.css'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const formRef = ref<FormInstance>()
const editorRef = ref<HTMLElement>()
const vditor = ref<Vditor>()
const saving = ref(false)
const publishing = ref(false)
const categories = ref<Category[]>([])
const tags = ref<Tag[]>([])
const autoSaveTimer = ref<number>()
const lastSaveTime = ref<Date>()

// 表单数据
const form = reactive<ArticleForm>({
  title: '',
  content: '',
  summary: '',
  cover: '',
  status: 'draft',
  categoryId: '',
  tagIds: [],
  isTop: false,
  publishedAt: ''
})

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 初始化编辑器
const initEditor = async () => {
  await nextTick()

  if (!editorRef.value) return

  vditor.value = new Vditor(editorRef.value, {
    height: 600,
    mode: 'ir', // 即时渲染模式，提供更好的编辑体验
    placeholder: '请输入文章内容，支持Markdown语法...',
    theme: 'classic',
    icon: 'ant',
    preview: {
      theme: {
        current: 'light',
        path: 'https://cdn.jsdelivr.net/npm/vditor/dist/css/content-theme'
      },
      hljs: {
        enable: true,
        lineNumber: true,
        style: 'github'
      },
      math: {
        inlineDigit: true,
        engine: 'KaTeX'
      }
    },
    toolbar: [
      'emoji',
      'headings',
      'bold',
      'italic',
      'strike',
      'link',
      '|',
      'list',
      'ordered-list',
      'check',
      'outdent',
      'indent',
      '|',
      'quote',
      'line',
      'code',
      'inline-code',
      'insert-before',
      'insert-after',
      '|',
      'table',
      'upload',
      '|',
      'undo',
      'redo',
      '|',
      'fullscreen',
      'edit-mode',
      {
        name: 'more',
        toolbar: [
          'both',
          'code-theme',
          'content-theme',
          'export',
          'outline',
          'preview',
          'devtools'
        ]
      }
    ],
    counter: {
      enable: true,
      type: 'markdown'
    },
    cache: {
      enable: false // 禁用缓存，避免数据混乱
    },
    upload: {
      url: '/api/upload/image',
      max: 10 * 1024 * 1024, // 10MB
      accept: 'image/*,.jpg,.jpeg,.png,.gif,.webp',
      multiple: true,
      fieldName: 'file',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin-token') || ''}`
      },
      success: (editor, msg) => {
        try {
          const response = JSON.parse(msg)
          if (response.code === 200) {
            ElMessage.success('图片上传成功')
          } else {
            ElMessage.error(response.message || '图片上传失败')
          }
        } catch (error) {
          ElMessage.success('图片上传成功')
        }
      },
      error: (msg) => {
        ElMessage.error('图片上传失败: ' + msg)
      }
    },
    resize: {
      enable: true,
      position: 'bottom'
    },
    outline: {
      enable: true,
      position: 'left'
    },
    after: () => {
      // 编辑器初始化完成后设置内容
      if (form.content) {
        vditor.value?.setValue(form.content)
      }

      // 添加快捷键支持
      vditor.value?.vditor.element.addEventListener('keydown', (event: KeyboardEvent) => {
        // Ctrl+S 保存草稿
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
          event.preventDefault()
          saveDraft()
        }

        // Ctrl+Shift+P 发布文章
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'P') {
          event.preventDefault()
          publish()
        }
      })

      // 监听编辑器内容变化
      vditor.value?.focus()
    },
    input: (value) => {
      form.content = value
      // 自动保存草稿（可选）
      autoSaveDraft()
    },
    blur: () => {
      // 失去焦点时自动保存草稿
      autoSaveDraft()
    }
  })
}

// 自动保存草稿功能
const autoSaveDraft = () => {
  // 清除之前的定时器
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }

  // 设置新的定时器，3秒后自动保存
  autoSaveTimer.value = window.setTimeout(async () => {
    if (form.title.trim() || form.content.trim()) {
      try {
        await saveDraftSilently()
        lastSaveTime.value = new Date()
      } catch (error) {
        console.error('自动保存失败:', error)
      }
    }
  }, 3000)
}

// 静默保存草稿（不显示成功消息）
const saveDraftSilently = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    console.log('草稿已自动保存')
  } catch (error) {
    throw error
  }
}

// 获取分类和标签
const fetchCategoriesAndTags = async () => {
  try {
    // 并行获取分类和标签
    const [categoriesResponse, tagsResponse] = await Promise.all([
      categoryApi.getAllCategories(),
      tagApi.getAllTags()
    ])

    if (categoriesResponse.code === 200 && categoriesResponse.data) {
      categories.value = categoriesResponse.data.map(transformCategoryVOToCategory)
    }

    if (tagsResponse.code === 200 && tagsResponse.data) {
      tags.value = tagsResponse.data.map(transformTagVOToTag)
    }
  } catch (error) {
    console.error('获取分类和标签失败:', error)
    ElMessage.error('获取分类和标签失败')
  }
}

// 获取文章详情（编辑模式）
const fetchArticle = async (id: string) => {
  try {
    const response = await articleApi.getArticleById(parseInt(id))
    if (response.code === 200 && response.data) {
      const article = transformArticleVOToArticle(response.data)

      // 设置表单数据
      form.title = article.title
      form.content = article.content
      form.summary = article.summary || ''
      form.cover = article.cover || ''
      form.status = article.status
      form.categoryId = article.categoryId
      form.tagIds = article.tags?.map(tag => tag.id) || []
      form.isTop = article.isTop
      form.publishedAt = article.publishedAt || ''

      // 设置编辑器内容
      if (vditor.value) {
        vditor.value.setValue(form.content)
      }
    } else {
      throw new Error(response.message || '获取文章详情失败')
    }
  } catch (error) {
    console.error('获取文章详情失败:', error)
    ElMessage.error('获取文章详情失败')
    router.push('/articles')
  }
}

// 保存草稿
const saveDraft = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    saving.value = true
    form.status = 'draft'

    // 构建请求数据
    const articleData: ArticleDTO = transformArticleToArticleDTO(form, authStore.user?.id || 1)

    let response
    if (isEdit.value) {
      // 更新文章
      response = await articleApi.updateArticle(parseInt(route.params.id as string), articleData)
    } else {
      // 创建文章
      response = await articleApi.createArticle(articleData)
    }

    if (response.code === 200) {
      ElMessage.success('草稿保存成功')
      lastSaveTime.value = new Date()

      // 如果是新建，跳转到编辑页面
      if (!isEdit.value && response.data) {
        router.replace(`/articles/edit/${response.data.id}`)
      }
    } else {
      throw new Error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败')
  } finally {
    saving.value = false
  }
}

// 发布文章
const publish = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    if (!form.content.trim()) {
      ElMessage.warning('请输入文章内容')
      return
    }

    publishing.value = true
    form.status = 'published'

    if (!form.publishedAt) {
      form.publishedAt = new Date().toISOString()
    }

    // 构建请求数据
    const articleData: ArticleDTO = transformArticleToArticleDTO(form, authStore.user?.id || 1)

    let response
    if (isEdit.value) {
      // 更新文章
      response = await articleApi.updateArticle(parseInt(route.params.id as string), articleData)
    } else {
      // 创建文章
      response = await articleApi.createArticle(articleData)
    }

    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '文章更新成功' : '文章发布成功')
      router.push('/articles')
    } else {
      throw new Error(response.message || '发布失败')
    }
  } catch (error) {
    console.error('发布文章失败:', error)
    ElMessage.error('发布文章失败')
  } finally {
    publishing.value = false
  }
}

// 返回列表
const goBack = async () => {
  // 检查是否有未保存的更改
  if (form.title || form.content) {
    try {
      await ElMessageBox.confirm(
        '有未保存的更改，确定要离开吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return
    }
  }

  router.push('/articles')
}

// 封面上传
const beforeCoverUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2
  
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleCoverSuccess = (response: any) => {
  form.cover = response.url
  ElMessage.success('封面上传成功')
}

// 格式化保存时间
const formatSaveTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  if (diff < 60000) { // 小于1分钟
    return '刚刚'
  } else if (diff < 3600000) { // 小于1小时
    return `${Math.floor(diff / 60000)}分钟前`
  } else {
    return time.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 组件挂载和卸载
onMounted(async () => {
  await fetchCategoriesAndTags()
  await initEditor()
  
  if (isEdit.value && route.params.id) {
    await fetchArticle(route.params.id as string)
  }
})

onUnmounted(() => {
  // 清理编辑器
  vditor.value?.destroy()

  // 清理自动保存定时器
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }
})
</script>

<style lang="scss" scoped>
.article-edit {
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .save-status {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 8px 12px;
        background: var(--el-fill-color-light);
        border-radius: 4px;

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
  
  .edit-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    
    .edit-main {
      .info-card {
        margin-bottom: 16px;
      }
      
      .editor-card {
        .editor-header {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .editor-tips {
            display: flex;
            gap: 8px;
          }
        }

        .editor-container {
          .vditor-editor {
            border: none;

            // 自定义Vditor样式
            :deep(.vditor) {
              border: 1px solid var(--el-border-color-light);
              border-radius: 6px;

              .vditor-toolbar {
                background: var(--el-bg-color);
                border-bottom: 1px solid var(--el-border-color-lighter);

                .vditor-toolbar__item {
                  &:hover {
                    background: var(--el-fill-color-light);
                  }
                }
              }

              .vditor-content {
                background: var(--el-bg-color);

                .vditor-ir {
                  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                  font-size: 14px;
                  line-height: 1.6;
                }

                .vditor-preview {
                  background: var(--el-bg-color);

                  h1, h2, h3, h4, h5, h6 {
                    color: var(--el-text-color-primary);
                  }

                  p, li {
                    color: var(--el-text-color-regular);
                  }

                  code {
                    background: var(--el-fill-color-light);
                    color: var(--el-color-danger);
                    padding: 2px 4px;
                    border-radius: 3px;
                  }

                  pre {
                    background: var(--el-fill-color-light);
                    border: 1px solid var(--el-border-color-lighter);
                    border-radius: 6px;
                  }

                  blockquote {
                    border-left: 4px solid var(--el-color-primary);
                    background: var(--el-fill-color-extra-light);
                    margin: 16px 0;
                    padding: 16px;
                  }

                  table {
                    border-collapse: collapse;

                    th, td {
                      border: 1px solid var(--el-border-color-light);
                      padding: 8px 12px;
                    }

                    th {
                      background: var(--el-fill-color-light);
                      font-weight: 600;
                    }
                  }
                }
              }

              .vditor-counter {
                background: var(--el-bg-color);
                color: var(--el-text-color-secondary);
                border-top: 1px solid var(--el-border-color-lighter);
              }
            }
          }
        }
      }
    }
    
    .edit-sidebar {
      .sidebar-card {
        margin-bottom: 16px;
        
        .cover-upload {
          .cover-uploader {
            :deep(.el-upload) {
              border: 1px dashed var(--el-border-color);
              border-radius: 6px;
              cursor: pointer;
              position: relative;
              overflow: hidden;
              transition: var(--el-transition-duration);
              width: 100%;
              height: 120px;
              display: flex;
              align-items: center;
              justify-content: center;
              
              &:hover {
                border-color: var(--el-color-primary);
              }
            }
            
            .cover-uploader-icon {
              font-size: 28px;
              color: #8c939d;
            }
            
            .cover-image {
              width: 100%;
              height: 120px;
              object-fit: cover;
              display: block;
            }
          }
          
          .cover-actions {
            margin-top: 8px;
            text-align: center;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .edit-container {
    grid-template-columns: 1fr;
    
    .edit-sidebar {
      order: -1;
    }
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-right {
      width: 100%;
      justify-content: flex-end;
      flex-wrap: wrap;

      .save-status {
        order: 3;
        width: 100%;
        justify-content: center;
        margin-top: 8px;
      }
    }
  }

  .edit-container {
    .edit-main {
      .editor-card {
        .editor-container {
          .vditor-editor {
            :deep(.vditor) {
              .vditor-toolbar {
                flex-wrap: wrap;

                .vditor-toolbar__item {
                  min-width: 32px;
                  height: 32px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

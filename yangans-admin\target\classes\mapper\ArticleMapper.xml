<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yangans.admin.mapper.ArticleMapper">

    <!-- 文章结果映射 -->
    <resultMap id="BaseResultMap" type="com.yangans.admin.domain.entity.Article">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="summary" property="summary" />
        <result column="cover" property="cover" />
        <result column="status" property="status" />
        <result column="category_id" property="categoryId" />
        <result column="author_id" property="authorId" />
        <result column="view_count" property="viewCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="is_top" property="isTop" />
        <result column="published_at" property="publishedAt" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 文章详情结果映射（包含关联信息） -->
    <resultMap id="ArticleWithRelationsMap" type="com.yangans.admin.domain.entity.Article" extends="BaseResultMap">
        <!-- 分类信息 -->
        <association property="category" javaType="com.yangans.admin.domain.entity.Category">
            <id column="category_id" property="id" />
            <result column="category_name" property="name" />
            <result column="category_description" property="description" />
            <result column="category_sort" property="sort" />
            <result column="category_article_count" property="articleCount" />
        </association>
        
        <!-- 作者信息 -->
        <association property="author" javaType="com.yangans.admin.domain.entity.User">
            <id column="author_id" property="id" />
            <result column="author_username" property="username" />
            <result column="author_nickname" property="nickname" />
            <result column="author_email" property="email" />
            <result column="author_avatar" property="avatar" />
            <result column="author_role" property="role" />
        </association>
        
        <!-- 标签列表 -->
        <collection property="tags" ofType="com.yangans.admin.domain.entity.Tag">
            <id column="tag_id" property="id" />
            <result column="tag_name" property="name" />
            <result column="tag_color" property="color" />
            <result column="tag_use_count" property="useCount" />
        </collection>
    </resultMap>

    <!-- 分页查询文章列表（包含关联信息） -->
    <select id="selectArticlePageWithRelations" resultMap="ArticleWithRelationsMap">
        SELECT 
            a.id, a.title, a.content, a.summary, a.cover, a.status,
            a.category_id, a.author_id, a.view_count, a.like_count, a.comment_count,
            a.is_top, a.published_at, a.created_at, a.updated_at,
            c.name as category_name, c.description as category_description, 
            c.sort as category_sort, c.article_count as category_article_count,
            u.username as author_username, u.nickname as author_nickname,
            u.email as author_email, u.avatar as author_avatar, u.role as author_role,
            t.id as tag_id, t.name as tag_name, t.color as tag_color, t.use_count as tag_use_count
        FROM articles a
        LEFT JOIN categories c ON a.category_id = c.id AND c.deleted = 0
        LEFT JOIN users u ON a.author_id = u.id AND u.deleted = 0
        LEFT JOIN article_tags at ON a.id = at.article_id
        LEFT JOIN tags t ON at.tag_id = t.id AND t.deleted = 0
        WHERE a.deleted = 0
        <if test="title != null and title != ''">
            AND a.title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="status != null and status != ''">
            AND a.status = #{status}
        </if>
        <if test="categoryId != null">
            AND a.category_id = #{categoryId}
        </if>
        ORDER BY a.is_top DESC, a.created_at DESC
    </select>

    <!-- 根据ID查询文章详情（包含关联信息） -->
    <select id="selectArticleWithRelationsById" resultMap="ArticleWithRelationsMap">
        SELECT 
            a.id, a.title, a.content, a.summary, a.cover, a.status,
            a.category_id, a.author_id, a.view_count, a.like_count, a.comment_count,
            a.is_top, a.published_at, a.created_at, a.updated_at,
            c.name as category_name, c.description as category_description, 
            c.sort as category_sort, c.article_count as category_article_count,
            u.username as author_username, u.nickname as author_nickname,
            u.email as author_email, u.avatar as author_avatar, u.role as author_role,
            t.id as tag_id, t.name as tag_name, t.color as tag_color, t.use_count as tag_use_count
        FROM articles a
        LEFT JOIN categories c ON a.category_id = c.id AND c.deleted = 0
        LEFT JOIN users u ON a.author_id = u.id AND u.deleted = 0
        LEFT JOIN article_tags at ON a.id = at.article_id
        LEFT JOIN tags t ON at.tag_id = t.id AND t.deleted = 0
        WHERE a.id = #{id} AND a.deleted = 0
    </select>

    <!-- 查询热门文章 -->
    <select id="selectHotArticles" resultMap="BaseResultMap">
        SELECT * FROM articles 
        WHERE status = 'PUBLISHED' AND deleted = 0
        ORDER BY view_count DESC, like_count DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询最新文章 -->
    <select id="selectLatestArticles" resultMap="BaseResultMap">
        SELECT * FROM articles 
        WHERE status = 'PUBLISHED' AND deleted = 0
        ORDER BY published_at DESC, created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询推荐文章 -->
    <select id="selectRecommendedArticles" resultMap="BaseResultMap">
        SELECT * FROM articles 
        WHERE status = 'PUBLISHED' AND is_top = 1 AND deleted = 0
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据分类ID查询文章列表 -->
    <select id="selectArticlesByCategoryId" resultMap="BaseResultMap">
        SELECT * FROM articles 
        WHERE category_id = #{categoryId} AND status = 'PUBLISHED' AND deleted = 0
        ORDER BY is_top DESC, created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据标签ID查询文章列表 -->
    <select id="selectArticlesByTagId" resultMap="BaseResultMap">
        SELECT a.* FROM articles a
        INNER JOIN article_tags at ON a.id = at.article_id
        WHERE at.tag_id = #{tagId} AND a.status = 'PUBLISHED' AND a.deleted = 0
        ORDER BY a.is_top DESC, a.created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 搜索文章 -->
    <select id="searchArticles" resultMap="BaseResultMap">
        SELECT * FROM articles 
        WHERE deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY is_top DESC, created_at DESC
    </select>

    <!-- 增加文章浏览量 -->
    <update id="incrementViewCount">
        UPDATE articles SET view_count = view_count + 1 WHERE id = #{id}
    </update>

    <!-- 增加文章点赞数 -->
    <update id="incrementLikeCount">
        UPDATE articles SET like_count = like_count + 1 WHERE id = #{id}
    </update>

    <!-- 更新文章评论数 -->
    <update id="updateCommentCount">
        UPDATE articles SET comment_count = #{commentCount} WHERE id = #{id}
    </update>

    <!-- 获取文章统计信息 -->
    <select id="getArticleStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 'PUBLISHED' THEN 1 ELSE 0 END) as published_count,
            SUM(CASE WHEN status = 'DRAFT' THEN 1 ELSE 0 END) as draft_count,
            SUM(view_count) as total_views,
            SUM(like_count) as total_likes,
            SUM(comment_count) as total_comments
        FROM articles 
        WHERE deleted = 0
    </select>

</mapper>

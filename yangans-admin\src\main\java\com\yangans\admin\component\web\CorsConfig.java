package com.yangans.admin.component.web;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.beans.factory.annotation.Value;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * CORS跨域配置
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Configuration
public class CorsConfig {

    @Value("${yangans.cors.allowed-origins:http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003}")
    private List<String> allowedOrigins;

    @Value("${yangans.cors.allowed-methods:GET,POST,PUT,DELETE,OPTIONS}")
    private String allowedMethods;

    @Value("${yangans.cors.allowed-headers:*}")
    private String allowedHeaders;

    @Value("${yangans.cors.allow-credentials:true}")
    private Boolean allowCredentials;

    @Value("${yangans.cors.max-age:3600}")
    private Long maxAge;

    // Getters and Setters
    public List<String> getAllowedOrigins() {
        return allowedOrigins;
    }

    public void setAllowedOrigins(List<String> allowedOrigins) {
        this.allowedOrigins = allowedOrigins;
    }

    public String getAllowedMethods() {
        return allowedMethods;
    }

    public void setAllowedMethods(String allowedMethods) {
        this.allowedMethods = allowedMethods;
    }

    public String getAllowedHeaders() {
        return allowedHeaders;
    }

    public void setAllowedHeaders(String allowedHeaders) {
        this.allowedHeaders = allowedHeaders;
    }

    public Boolean getAllowCredentials() {
        return allowCredentials;
    }

    public void setAllowCredentials(Boolean allowCredentials) {
        this.allowCredentials = allowCredentials;
    }

    public Long getMaxAge() {
        return maxAge;
    }

    public void setMaxAge(Long maxAge) {
        this.maxAge = maxAge;
    }

    /**
     * CORS配置源
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许的源
        configuration.setAllowedOrigins(allowedOrigins);
        
        // 允许的方法
        configuration.setAllowedMethods(Arrays.asList(allowedMethods.split(",")));
        
        // 允许的头部
        if ("*".equals(allowedHeaders)) {
            configuration.addAllowedHeader("*");
        } else {
            configuration.setAllowedHeaders(Arrays.asList(allowedHeaders.split(",")));
        }
        
        // 是否允许凭证
        configuration.setAllowCredentials(allowCredentials);
        
        // 预检请求的缓存时间
        configuration.setMaxAge(maxAge);
        
        // 暴露的头部
        configuration.setExposedHeaders(Arrays.asList(
                "Authorization",
                "Content-Type",
                "X-Requested-With",
                "Accept",
                "Origin",
                "Access-Control-Request-Method",
                "Access-Control-Request-Headers"
        ));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        
        // 主站API的CORS配置（更宽松）
        CorsConfiguration publicConfig = new CorsConfiguration(configuration);
        publicConfig.setAllowedOrigins(Collections.singletonList("*")); // 主站允许所有源
        publicConfig.setAllowCredentials(false); // 主站不需要凭证
        source.registerCorsConfiguration("/api/public/**", publicConfig);
        
        // 管理后台API的CORS配置（更严格）
        CorsConfiguration adminConfig = new CorsConfiguration(configuration);
        adminConfig.setAllowedOrigins(allowedOrigins); // 管理后台只允许配置的源
        adminConfig.setAllowCredentials(true); // 管理后台需要凭证
        source.registerCorsConfiguration("/api/admin/**", adminConfig);
        
        return source;
    }
}

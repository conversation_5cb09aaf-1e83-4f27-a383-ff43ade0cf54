<template>
  <div class="article-list">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">文章管理</h1>
        <p class="page-subtitle">管理网站文章内容</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="createArticle">
          新建文章
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索标题或内容"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分类">
          <el-select v-model="searchForm.categoryId" placeholder="选择分类" clearable style="width: 150px">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button :icon="Refresh" @click="resetSearch">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 文章列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="articles"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="title" label="标题" min-width="200">
          <template #default="{ row }">
            <div class="article-title">
              <span>{{ row.title }}</span>
              <el-tag v-if="row.isTop" type="warning" size="small" class="top-tag">
                置顶
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ row.category?.name || '未分类' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="viewCount" label="浏览量" width="100" />
        <el-table-column prop="commentCount" label="评论" width="80" />

        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column prop="updatedAt" label="更新时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" text size="small" @click="editArticle(row.id)">
              编辑
            </el-button>
            <el-button type="success" text size="small" @click="viewArticle(row.id)">
              预览
            </el-button>
            <el-button v-if="row.status === 'draft'" type="warning" text size="small" @click="publishSingleArticle(row)">
              发布
            </el-button>
            <el-button v-if="row.status === 'published'" type="info" text size="small" @click="unpublishSingleArticle(row)">
              撤回
            </el-button>
            <el-button v-if="!row.isTop" type="primary" text size="small" @click="topSingleArticle(row)">
              置顶
            </el-button>
            <el-button v-if="row.isTop" type="info" text size="small" @click="untopSingleArticle(row)">
              取消置顶
            </el-button>
            <el-button type="danger" text size="small" @click="deleteArticle(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div v-if="selectedArticles.length > 0" class="batch-actions">
        <span class="selected-info">已选择 {{ selectedArticles.length }} 项</span>
        <el-button type="danger" size="small" @click="batchDelete">
          批量删除
        </el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 文章预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="文章预览"
      width="80%"
      :before-close="closePreview"
      class="article-preview-dialog"
    >
      <div v-if="previewArticle" class="preview-container">
        <!-- 文章头部信息 -->
        <div class="preview-header">
          <h1 class="preview-title">{{ previewArticle.title }}</h1>
          <div class="preview-meta">
            <el-tag type="primary" size="small">{{ previewArticle.category }}</el-tag>
            <el-tag
              v-for="tag in previewArticle.tags"
              :key="tag"
              size="small"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <span class="meta-item">
              <el-icon><Clock /></el-icon>
              {{ formatDate(previewArticle.createdAt) }}
            </span>
            <span class="meta-item">
              <el-icon><View /></el-icon>
              {{ previewArticle.viewCount }} 次浏览
            </span>
          </div>
        </div>

        <!-- 文章内容 -->
        <div class="preview-content">
          <div ref="previewContentRef" class="markdown-content"></div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="editCurrentArticle">
            <el-icon><Edit /></el-icon>
            编辑文章
          </el-button>
          <el-button type="success" @click="openInNewTab">
            <el-icon><Link /></el-icon>
            新窗口打开
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Refresh, Clock, View, Edit, Link } from '@element-plus/icons-vue'
import type { Article, Category } from '@/types'
import { articleApi, type ArticleVO, type ArticleQueryDTO } from '@/api/article'
import { categoryApi } from '@/api/category'
import { transformArticleVOToArticle, transformCategoryVOToCategory, getStatusText, getStatusType } from '@/utils/dataTransform'
import { useAuthStore } from '@/stores/auth'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const articles = ref<Article[]>([])
const categories = ref<Category[]>([])
const selectedArticles = ref<Article[]>([])

// 预览相关
const previewVisible = ref(false)
const previewArticle = ref<Article | null>(null)
const previewContentRef = ref<HTMLElement>()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  categoryId: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 获取文章列表
const fetchArticles = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const queryParams: ArticleQueryDTO = {
      current: pagination.page,
      size: pagination.pageSize,
      title: searchForm.keyword || undefined,
      status: searchForm.status || undefined,
      categoryId: searchForm.categoryId ? parseInt(searchForm.categoryId) : undefined,
      orderBy: 'created_at',
      orderDirection: 'desc'
    }

    // 调用API
    const response = await articleApi.getArticles(queryParams)

    if (response.code === 200 && response.data) {
      // 转换数据格式
      articles.value = response.data.records.map(transformArticleVOToArticle)
      pagination.total = response.data.total
    } else {
      throw new Error(response.message || '获取文章列表失败')
    }
  } catch (error) {
    console.error('获取文章列表失败:', error)
    ElMessage.error('获取文章列表失败')
    // 设置空数据
    articles.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await categoryApi.getAllCategories()
    if (response.code === 200 && response.data) {
      categories.value = response.data.map(transformCategoryVOToCategory)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    categories.value = []
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchArticles()
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    categoryId: ''
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchArticles()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchArticles()
}

// 选择处理
const handleSelectionChange = (selection: Article[]) => {
  selectedArticles.value = selection
}

// 操作方法
const createArticle = () => {
  router.push('/articles/create')
}

const editArticle = (id: string) => {
  router.push(`/articles/edit/${id}`)
}

const viewArticle = async (id: string) => {
  try {
    // 查找文章数据
    const article = articles.value.find(a => a.id === id)
    if (!article) {
      ElMessage.error('文章不存在')
      return
    }

    previewArticle.value = article
    previewVisible.value = true

    // 等待DOM更新后渲染Markdown内容
    await nextTick()
    renderMarkdownContent()
  } catch (error) {
    console.error('预览文章失败:', error)
    ElMessage.error('预览文章失败')
  }
}

// 渲染Markdown内容
const renderMarkdownContent = () => {
  if (!previewContentRef.value || !previewArticle.value) return

  // 简单的Markdown渲染（实际项目中可以使用marked或其他库）
  const content = previewArticle.value.content || '暂无内容'

  // 基础的Markdown转HTML（这里简化处理）
  let html = content
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    .replace(/!\[([^\]]*)\]\(([^\)]*)\)/gim, '<img alt="$1" src="$2" style="max-width: 100%; height: auto;" />')
    .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2" target="_blank">$1</a>')
    .replace(/\n/gim, '<br>')
    .replace(/```([^`]*)```/gim, '<pre><code>$1</code></pre>')
    .replace(/`([^`]*)`/gim, '<code>$1</code>')

  previewContentRef.value.innerHTML = html
}

// 关闭预览
const closePreview = () => {
  previewVisible.value = false
  previewArticle.value = null
}

// 编辑当前预览的文章
const editCurrentArticle = () => {
  if (previewArticle.value) {
    router.push(`/articles/edit/${previewArticle.value.id}`)
    closePreview()
  }
}

// 在新窗口打开文章
const openInNewTab = () => {
  if (previewArticle.value) {
    window.open(`/articles/${previewArticle.value.id}`, '_blank')
  }
}

const deleteArticle = async (article: Article) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文章"${article.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用删除API
    const response = await articleApi.deleteArticle(parseInt(article.id))
    if (response.code === 200) {
      ElMessage.success('删除成功')
      fetchArticles()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error.message) {
      console.error('删除文章失败:', error)
      ElMessage.error(error.message)
    }
    // 用户取消时不显示错误
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedArticles.value.length} 篇文章吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用批量删除API
    const ids = selectedArticles.value.map(article => parseInt(article.id))
    const response = await articleApi.batchDeleteArticles(ids)
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      selectedArticles.value = []
      fetchArticles()
    } else {
      throw new Error(response.message || '批量删除失败')
    }
  } catch (error: any) {
    if (error.message) {
      console.error('批量删除文章失败:', error)
      ElMessage.error(error.message)
    }
    // 用户取消时不显示错误
  }
}

// 发布单篇文章
const publishSingleArticle = async (article: Article) => {
  try {
    const response = await articleApi.publishArticle(parseInt(article.id))
    if (response.code === 200) {
      ElMessage.success('文章发布成功')
      fetchArticles()
    } else {
      throw new Error(response.message || '发布失败')
    }
  } catch (error: any) {
    console.error('发布文章失败:', error)
    ElMessage.error(error.message || '发布文章失败')
  }
}

// 撤回单篇文章
const unpublishSingleArticle = async (article: Article) => {
  try {
    const response = await articleApi.unpublishArticle(parseInt(article.id))
    if (response.code === 200) {
      ElMessage.success('文章撤回成功')
      fetchArticles()
    } else {
      throw new Error(response.message || '撤回失败')
    }
  } catch (error: any) {
    console.error('撤回文章失败:', error)
    ElMessage.error(error.message || '撤回文章失败')
  }
}

// 置顶单篇文章
const topSingleArticle = async (article: Article) => {
  try {
    const response = await articleApi.topArticle(parseInt(article.id))
    if (response.code === 200) {
      ElMessage.success('文章置顶成功')
      fetchArticles()
    } else {
      throw new Error(response.message || '置顶失败')
    }
  } catch (error: any) {
    console.error('置顶文章失败:', error)
    ElMessage.error(error.message || '置顶文章失败')
  }
}

// 取消置顶单篇文章
const untopSingleArticle = async (article: Article) => {
  try {
    const response = await articleApi.untopArticle(parseInt(article.id))
    if (response.code === 200) {
      ElMessage.success('取消置顶成功')
      fetchArticles()
    } else {
      throw new Error(response.message || '取消置顶失败')
    }
  } catch (error: any) {
    console.error('取消置顶失败:', error)
    ElMessage.error(error.message || '取消置顶失败')
  }
}

// 工具方法
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCategories()
  fetchArticles()
})
</script>

<style lang="scss" scoped>
.article-list {
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .header-left {
      .page-title {
        margin: 0 0 4px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .page-subtitle {
        margin: 0;
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
    
    :deep(.el-card__body) {
      padding: 16px;
    }
  }
  
  .table-card {
    .article-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .top-tag {
        flex-shrink: 0;
      }
    }
    
    .batch-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;
      border-top: 1px solid var(--el-border-color-lighter);
      margin-top: 16px;
      
      .selected-info {
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

// 文章预览对话框样式
.article-preview-dialog {
  .preview-container {
    max-height: 70vh;
    overflow-y: auto;

    .preview-header {
      border-bottom: 1px solid var(--el-border-color-light);
      padding-bottom: 20px;
      margin-bottom: 20px;

      .preview-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin: 0 0 16px 0;
        line-height: 1.4;
      }

      .preview-meta {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;

        .tag-item {
          margin-left: 8px;
        }

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          color: var(--el-text-color-secondary);
          font-size: 14px;

          .el-icon {
            font-size: 16px;
          }
        }
      }
    }

    .preview-content {
      .markdown-content {
        line-height: 1.8;
        color: var(--el-text-color-regular);

        h1, h2, h3, h4, h5, h6 {
          color: var(--el-text-color-primary);
          margin: 20px 0 16px 0;
          font-weight: 600;
        }

        h1 { font-size: 28px; }
        h2 { font-size: 24px; }
        h3 { font-size: 20px; }

        p {
          margin: 16px 0;
        }

        img {
          max-width: 100%;
          height: auto;
          border-radius: 6px;
          margin: 16px 0;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        code {
          background: var(--el-fill-color-light);
          color: var(--el-color-danger);
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
        }

        pre {
          background: var(--el-fill-color-light);
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 6px;
          padding: 16px;
          overflow-x: auto;
          margin: 16px 0;

          code {
            background: none;
            color: var(--el-text-color-primary);
            padding: 0;
          }
        }

        a {
          color: var(--el-color-primary);
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        strong {
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        em {
          font-style: italic;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-card {
    :deep(.el-form--inline) {
      .el-form-item {
        display: block;
        margin-bottom: 16px;
        margin-right: 0;
      }
    }
  }

  .article-preview-dialog {
    .preview-container {
      .preview-header {
        .preview-meta {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }

    .dialog-footer {
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>

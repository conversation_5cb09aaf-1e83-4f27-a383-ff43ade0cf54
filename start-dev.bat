@echo off
echo ========================================
echo YangAns博客系统 - 开发环境启动脚本
echo ========================================
echo.

echo 检查环境依赖...

:: 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Java环境，请安装JDK 8或更高版本
    pause
    exit /b 1
)

:: 检查Node.js环境
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Node.js环境，请安装Node.js 16或更高版本
    pause
    exit /b 1
)

:: 检查MySQL服务
echo 检查MySQL服务状态...
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] MySQL服务未启动，请确保MySQL服务正在运行
    echo 数据库连接信息：
    echo   - 主机: localhost:3306
    echo   - 数据库: yangans_blog
    echo   - 用户名: root
    echo   - 密码: 123456
    echo.
)

:: 检查Redis服务（可选）
echo 检查Redis服务状态...
sc query redis >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] Redis服务未启动，缓存功能可能不可用
    echo.
)

echo 环境检查完成！
echo.

echo ========================================
echo 启动后端服务 (yangans-admin)
echo ========================================
echo 端口: 8080
echo API地址: http://localhost:8080/api
echo Swagger文档: http://localhost:8080/api/swagger-ui/
echo.

:: 启动后端服务
start "YangAns后端服务" cmd /k "cd /d yangans-admin && mvn spring-boot:run"

echo 等待后端服务启动...
timeout /t 10 /nobreak >nul

echo ========================================
echo 启动前端管理后台 (admin-panel)
echo ========================================
echo 端口: 3002
echo 访问地址: http://localhost:3002
echo.

:: 启动前端管理后台
start "YangAns管理后台" cmd /k "cd /d yangans-web\admin-panel && npm run dev"

echo 等待前端服务启动...
timeout /t 5 /nobreak >nul

echo ========================================
echo 启动前端主站 (main-site)
echo ========================================
echo 端口: 3000
echo 访问地址: http://localhost:3000
echo.

:: 启动前端主站
start "YangAns主站" cmd /k "cd /d yangans-web\main-site && npm run dev"

echo.
echo ========================================
echo 🎉 所有服务启动完成！
echo ========================================
echo.
echo 📋 服务信息：
echo   后端API服务:     http://localhost:8080/api
echo   管理后台:        http://localhost:3002
echo   主站:           http://localhost:3000
echo   API文档:        http://localhost:8080/api/swagger-ui/
echo.
echo 📝 测试建议：
echo   1. 首先访问管理后台测试文章管理功能
echo   2. 使用API测试页面验证前后端连接
echo   3. 检查浏览器控制台是否有错误信息
echo.
echo 🔧 如果遇到问题：
echo   1. 检查端口是否被占用
echo   2. 确认数据库连接正常
echo   3. 查看各服务的控制台输出
echo.
echo 按任意键退出...
pause >nul

import type { ArticleVO, CategoryVO, TagVO } from '@/api/article'
import type { Article, Category, Tag } from '@/types'

// 状态映射
const STATUS_MAP = {
  // 后端 -> 前端
  'DRAFT': 'draft',
  'PUBLISHED': 'published',
  'ARCHIVED': 'archived',
  // 前端 -> 后端
  'draft': 'DRAFT',
  'published': 'PUBLISHED',
  'archived': 'ARCHIVED'
} as const

/**
 * 将后端ArticleVO转换为前端Article类型
 */
export function transformArticleVOToArticle(vo: ArticleVO): Article {
  return {
    id: vo.id.toString(),
    title: vo.title,
    content: vo.content,
    summary: vo.summary,
    cover: vo.cover,
    status: STATUS_MAP[vo.status as keyof typeof STATUS_MAP] as 'draft' | 'published' | 'archived',
    categoryId: vo.categoryId?.toString() || '',
    category: vo.category ? transformCategoryVOToCategory(vo.category) : undefined,
    tags: vo.tags?.map(transformTagVOToTag) || [],
    author: {
      id: vo.author?.id.toString() || '',
      username: vo.author?.username || '',
      email: vo.author?.email || '',
      role: vo.author?.role === 'ADMIN' ? 'admin' : 'user',
      createdAt: vo.author?.createdAt || '',
      updatedAt: vo.author?.updatedAt || ''
    },
    viewCount: vo.viewCount,
    likeCount: vo.likeCount,
    commentCount: vo.commentCount,
    isTop: vo.isTop,
    publishedAt: vo.publishedAt,
    createdAt: vo.createdAt,
    updatedAt: vo.updatedAt
  }
}

/**
 * 将前端Article转换为后端ArticleDTO
 */
export function transformArticleToArticleDTO(article: Partial<Article>, authorId: number): any {
  return {
    id: article.id ? parseInt(article.id) : undefined,
    title: article.title,
    content: article.content,
    summary: article.summary,
    cover: article.cover,
    status: article.status ? STATUS_MAP[article.status as keyof typeof STATUS_MAP] : undefined,
    categoryId: article.categoryId ? parseInt(article.categoryId) : undefined,
    authorId: authorId,
    isTop: article.isTop,
    publishedAt: article.publishedAt,
    tagIds: article.tags?.map(tag => parseInt(tag.id)) || []
  }
}

/**
 * 将后端CategoryVO转换为前端Category类型
 */
export function transformCategoryVOToCategory(vo: CategoryVO): Category {
  return {
    id: vo.id.toString(),
    name: vo.name,
    description: vo.description,
    sort: vo.sort,
    articleCount: vo.articleCount,
    createdAt: vo.createdAt,
    updatedAt: vo.updatedAt
  }
}

/**
 * 将前端Category转换为后端CategoryDTO
 */
export function transformCategoryToCategoryDTO(category: Partial<Category>): any {
  return {
    id: category.id ? parseInt(category.id) : undefined,
    name: category.name,
    description: category.description,
    sort: category.sort
  }
}

/**
 * 将后端TagVO转换为前端Tag类型
 */
export function transformTagVOToTag(vo: TagVO): Tag {
  return {
    id: vo.id.toString(),
    name: vo.name,
    color: vo.color,
    articleCount: vo.useCount,
    createdAt: vo.createdAt,
    updatedAt: vo.updatedAt
  }
}

/**
 * 将前端Tag转换为后端TagDTO
 */
export function transformTagToTagDTO(tag: Partial<Tag>): any {
  return {
    id: tag.id ? parseInt(tag.id) : undefined,
    name: tag.name,
    color: tag.color
  }
}

/**
 * 获取状态显示文本
 */
export function getStatusText(status: string): string {
  const statusTextMap: Record<string, string> = {
    'draft': '草稿',
    'published': '已发布',
    'archived': '已归档',
    'DRAFT': '草稿',
    'PUBLISHED': '已发布',
    'ARCHIVED': '已归档'
  }
  return statusTextMap[status] || status
}

/**
 * 获取状态类型（用于Element Plus的tag组件）
 */
export function getStatusType(status: string): string {
  const statusTypeMap: Record<string, string> = {
    'draft': 'warning',
    'published': 'success',
    'archived': 'info',
    'DRAFT': 'warning',
    'PUBLISHED': 'success',
    'ARCHIVED': 'info'
  }
  return statusTypeMap[status] || 'info'
}

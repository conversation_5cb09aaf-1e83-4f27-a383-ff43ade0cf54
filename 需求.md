
# YangAns个人网站初期需求文档

**项目概述**：构建一个功能完整的个人网站，支持文章发布、生活记录、追番统计等个人化功能。

**技术栈**：Vue3 (前端) + SpringBoot (后端) + MySQL (数据库)

---

## 一、核心功能模块

### 1. 文章系统
- **功能描述**：
  - Markdown编辑器支持（含实时预览、语法高亮）
  - 文章分类/标签管理（支持多级分类）
  - 文章评论/评价功能（支持点赞、回复嵌套、表情包）
  - 文章搜索功能（标题、内容、标签搜索）
  - 文章阅读统计和热门文章推荐
  - 文章草稿保存和定时发布
  - 按照时间归档
- **技术实现**：
  - 前端：Vue3 + Vditor/Editor.md（Markdown编辑器）
  - 后端：SpringBoot + MyBatis-Plus
  - 数据库：文章表、分类表、标签表、评论表、等其他业务表

### 2. 留言墙
- **功能描述**：
  - 访客留言功能（支持匿名留言）
  - 留言审核机制（管理员审核后显示）
  - 留言回复功能（管理员可回复）
  - 留言表情包支持
  - 留言点赞功能
- **技术实现**：
  - 前端：Vue3组件化留言板
  - 后端：留言API接口
  - 数据库：留言表（包含审核状态字段）

### 3. 生活记录
- **功能描述**：
  - 时间轴形式展示生活动态
  - 支持图片、文字、位置信息
  - 心情标签和天气记录
  - 生活记录分类（工作、学习、娱乐等）
- **技术实现**：
  - 前端：时间轴组件、图片上传组件
  - 后端：生活记录CRUD接口
  - 数据库：生活记录表

### 4. 追番统计
- **功能描述**：
  - 番剧信息管理（名称、封面、简介、评分）
  - 观看状态跟踪（想看、在看、已看、弃番）
  - 个人评分和评价系统
  - 番剧推荐功能
  - 观看进度记录
  - 年度追番统计报告
- **技术实现**：
  - 前端：番剧卡片组件、进度条组件
  - 后端：番剧管理API
  - 数据库：番剧表、观看记录表
  - 第三方API：豆瓣API或Bangumi API获取番剧信息

---

## 二、扩展功能

### 1. 用户认证
- **管理员功能**：
  - JWT令牌验证（仅管理员可发布内容）
  - 管理员登录界面
  - 权限管理（内容发布、评论审核、用户管理）
- **访客功能**：
  - 社交账号登录（GitHub/微信/QQ）
  - 游客评论功能
  - 个人资料管理

### 2. 优化方案
- **SEO优化**：
  - 服务端渲染（SSR）支持
  - 网站地图自动生成
  - Meta标签优化
  - 结构化数据标记
- **性能优化**：
  - 图片懒加载和压缩
  - 代码分割和按需加载
  - CDN加速
  - 缓存策略（Redis缓存热门内容）
- **用户体验**：
  - 响应式设计（移动端适配）
  - 暗黑模式切换
  - 阅读进度条
  - 回到顶部功能

### 3. 多媒体支持
- **图片处理**：
  - 图床整合（阿里云OSS/MinIO/七牛云）
  - 图片压缩和格式转换
  - 图片水印添加
- **视频支持**：
  - 视频嵌入支持（B站/Youtube/优酷）
  - 视频封面提取
  - 视频播放统计

---

## 三、技术架构设计

### 前端架构
- **框架**：Vue3 + TypeScript
- **构建工具**：Vite
- **UI框架**：Element Plus / Ant Design Vue
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **HTTP客户端**：Axios
- **样式**：Sass/SCSS

### 后端架构
- **框架**：SpringBoot 2.7+
- **数据访问**：MyBatis-Plus
- **安全框架**：Spring Security + JWT
- **缓存**：Redis
- **文档**：Swagger/OpenAPI 3
- **日志**：Logback
- **任务调度**：Spring Task

### 数据库设计
- **主数据库**：MySQL 8.0
- **缓存数据库**：Redis
- **搜索引擎**：MySQL全文检索	初期无需Elasticsearch

---

## 四、数据库表结构

### 核心表设计
- **用户表**：用户信息
- **文章表**：文章内容、发布状态、统计信息
- **分类表**：文章分类管理
- **标签表**：文章标签管理
- **评论表**：评论内容
- **留言表**：留言墙数据
- **生活记录表**：生活动态数据
- **番剧表**：番剧基础信息
- **观看记录表**：追番进度和评价
- **其他表**

---

## 五、安全与性能

### 安全措施
- **身份认证**：JWT令牌验证
- **数据验证**：前后端双重数据校验
- **SQL注入防护**：参数化查询
- **XSS防护**：内容过滤和转义
- **CSRF防护**：Token验证
- **接口限流**：防止恶意请求

### 性能优化
- **数据库优化**：索引优化、查询优化
- **缓存策略**：Redis缓存热点数据
- **CDN加速**：静态资源CDN分发
- **图片优化**：WebP格式、懒加载
- **代码优化**：代码分割、Tree Shaking
- **监控告警**：系统性能监控

---

## 六、部署方案

### 开发环境
- **前端**：Vite开发服务器
- **后端**：SpringBoot内嵌Tomcat
- **数据库**：本地MySQL + Redis

### 生产环境
- **前端**：Nginx静态文件服务 + 反向代理
- **后端**：Docker容器化部署
- **数据库**：MySQL + Redis


---

## 七、开发计划

### 第一阶段（基础功能）
1. 项目初始化和环境搭建
2. 用户认证系统
3. 文章系统基础功能
4. 基础UI界面

### 第二阶段（核心功能）
1. 留言墙功能
2. 生活记录功能
3. 追番统计功能
4. 评论系统

### 第三阶段（优化完善）
1. 性能优化
2. SEO优化
3. 移动端适配
4. 多媒体支持

### 第四阶段（部署上线）
1. 生产环境部署
2. 监控和日志系统
3. 备份和恢复方案
4. 文档完善

---

### 操作入口方案

| 功能                | 操作位置       | 理由                                                                 |
|---------------------|---------------|----------------------------------------------------------------------|
| **文章发布/管理**    | 独立管理后台   | 完整编辑器+分类管理，操作复杂需专业界面                             |
| **生活记录发布**     | 独立管理后台   | 保证操作安全性和内容一致性                                           |
| **添加/管理番剧**    | 独立管理后台   | 低频操作，需要完整元数据管理界面                                     |
| **追番进度更新**     | **主站点**    | 高频操作，登录后可直接在卡片更新（需管理员登录）                     |
| **文章评论**         | 主站点        | 访客互动行为（您的账号可优先展示）                                   |
| **留言提交**         | 主站点        | 核心用户互动功能                                                     |
| **内容审核**         | 管理后台      | 需要列表视图和批量操作                                               |

### 架构调整说明
```mermaid
graph TD
    A[访客] -->|浏览| B(主站点)
    A -->|留言/评论| C[互动功能]
    
    D[管理员] -->|登录主站| E{操作判断}
    E -->|追番进度| F[直接更新]
    
    D -->|登录后台| G[管理后台]
    G --> H[文章管理]
    G --> I[生活记录]
    G --> J[番剧管理]
    G --> K[审核中心]
    
    F -->|进度更新| M[数据库]
    H --> M
    I --> M
    J --> M
```

### 关键调整细节

1. **认证系统简化**
   - 移除注册功能，仅保留管理员登录
   - 主站显示「管理员入口」按钮（普通访客不可见）


2. **追番进度特殊处理**
   - 主站番剧卡片集成进度控件（仅管理员可见）


3. **管理后台强化**
   - 增加生活记录快速发布面板
   - 集成文章草稿管理
   - 番剧信息批量导入


4. **访客互动优化**
   - 评论/留言显示「博主」标识：


### 安全增强方案

1. **主站操作保护示例**
   ```java
   // 追番进度更新接口
   @PatchMapping("/animes/progress")
   @PreAuthorize("isAuthenticated()") // 必须管理员登录
   public void updateProgress(@RequestBody ProgressDTO dto) {
       // 验证当前用户是唯一管理员
       if (!SecurityUtils.isAdmin()) throw new AccessDeniedException();
       animeService.updateProgress(dto);
   }
   ```

2. **后台访问控制示例**
   - IP白名单（可选）
   - 登录会话30分钟过期
   ```yaml
   # application.yml
   security:
     jwt:
       expiration: 1800 # 30分钟
   ```

3. **操作审计**
   - 记录关键操作日志示例：
   ```java
   @Asynchronous
   public void logAdminAction(ActionType type) {
       auditLogRepo.save(new AuditLog(
           SecurityUtils.getCurrentUsername(),
           type,
           LocalDateTime.now()
       ));
   }
   ```

### 界面优化建议

1. **主站管理员入口**
   - 右下角悬浮管理按钮（登录后显示）
   - 鼠标悬停显示快捷操作菜单

2. **追番卡片管理员视图**
   - 普通访客：只读视图
   - 管理员：显示进度选择器+更新按钮

### 调整后的优势

1. **安全隔离**：内容创作等高危操作完全隔离在后台
2. **操作高效**：高频的追番更新保留在主站一键操作
3. **体验统一**：您的评论/留言在主站自然展示（带博主标识）
4. **访客友好**：主站保持简洁，无多余管理元素
5. **维护简单**：单用户系统免除多用户权限复杂度

> **最终推荐方案**：  
> 开发独立的管理后台，主站仅保留：  
> 1. 追番进度更新（需登录）  
> 2. 您的评论/留言展示  

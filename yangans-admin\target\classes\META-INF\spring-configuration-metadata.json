{"groups": [{"name": "yangans.cors", "type": "com.yangans.admin.component.web.CorsConfig", "sourceType": "com.yangans.admin.component.web.CorsConfig"}], "properties": [{"name": "yangans.cors.allow-credentials", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.yangans.admin.component.web.CorsConfig"}, {"name": "yangans.cors.allowed-headers", "type": "java.lang.String", "sourceType": "com.yangans.admin.component.web.CorsConfig"}, {"name": "yangans.cors.allowed-methods", "type": "java.lang.String", "sourceType": "com.yangans.admin.component.web.CorsConfig"}, {"name": "yangans.cors.allowed-origins", "type": "java.util.List<java.lang.String>", "sourceType": "com.yangans.admin.component.web.CorsConfig"}, {"name": "yangans.cors.max-age", "type": "java.lang.Long", "sourceType": "com.yangans.admin.component.web.CorsConfig"}], "hints": []}
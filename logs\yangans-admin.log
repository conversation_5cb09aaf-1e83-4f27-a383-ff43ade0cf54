2025-07-30 00:21:18.023 [main] INFO  com.yangans.admin.YangAnsApplication - Starting YangAnsApplication using Java 1.8.0_131 on LAPTOP-O0M94MPC with PID 8460 (D:\yangans-website\yangans-admin\target\classes started by Yang<PERSON>ns in D:\yangans-website)
2025-07-30 00:21:18.027 [main] DEBUG com.yangans.admin.YangAnsApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-30 00:21:18.027 [main] INFO  com.yangans.admin.YangAnsApplication - The following 1 profile is active: "dev"
2025-07-30 00:21:18.789 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-30 00:21:18.790 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.yangans.admin'
2025-07-30 00:21:18.844 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 00:21:18.847 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 00:21:18.875 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-07-30 00:21:19.001 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleMapper.class]
2025-07-30 00:21:19.001 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleTagMapper.class]
2025-07-30 00:21:19.001 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\CategoryMapper.class]
2025-07-30 00:21:19.001 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\TagMapper.class]
2025-07-30 00:21:19.001 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\UserMapper.class]
2025-07-30 00:21:19.002 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleMapper' and 'com.yangans.admin.mapper.ArticleMapper' mapperInterface
2025-07-30 00:21:19.005 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleTagMapper' and 'com.yangans.admin.mapper.ArticleTagMapper' mapperInterface
2025-07-30 00:21:19.006 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'categoryMapper' and 'com.yangans.admin.mapper.CategoryMapper' mapperInterface
2025-07-30 00:21:19.007 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tagMapper' and 'com.yangans.admin.mapper.TagMapper' mapperInterface
2025-07-30 00:21:19.009 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.yangans.admin.mapper.UserMapper' mapperInterface
2025-07-30 00:21:19.565 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-30 00:21:19.575 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 00:21:19.575 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 00:21:19.733 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-30 00:21:19.733 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1662 ms
2025-07-30 00:21:20.748 [main] DEBUG c.y.a.component.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-30 00:21:21.490 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 60 mappings in 'requestMappingHandlerMapping'
2025-07-30 00:21:21.708 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-30 00:21:21.722 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/admin/auth/login']
2025-07-30 00:21:21.722 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-30 00:21:21.722 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-30 00:21:21.723 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources/**']
2025-07-30 00:21:21.723 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-30 00:21:21.723 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/actuator/**']
2025-07-30 00:21:21.723 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-30 00:21:21.723 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for Ant [pattern='/api/admin/**']
2025-07-30 00:21:21.723 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-07-30 00:21:21.731 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@54f69311, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ab86b2a, org.springframework.security.web.context.SecurityContextPersistenceFilter@4780341, org.springframework.security.web.header.HeaderWriterFilter@497921d0, org.springframework.security.web.authentication.logout.LogoutFilter@38923cfe, com.yangans.admin.component.security.JwtAuthenticationFilter@29b5533, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@612ac38b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7981963f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7793b55d, org.springframework.security.web.session.SessionManagementFilter@45790cb, org.springframework.security.web.access.ExceptionTranslationFilter@16ef1160, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4a2d66e]
2025-07-30 00:21:21.916 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-30 00:21:21.972 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-30 00:21:21.997 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui/**] in 'resourceHandlerMapping'
2025-07-30 00:21:22.006 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-30 00:21:22.025 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-30 00:21:22.266 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-30 00:21:22.713 [main] INFO  com.yangans.admin.YangAnsApplication - Started YangAnsApplication in 5.123 seconds (JVM running for 6.027)
2025-07-30 23:20:49.405 [main] INFO  com.yangans.admin.YangAnsApplication - Starting YangAnsApplication using Java 1.8.0_131 on LAPTOP-O0M94MPC with PID 43156 (D:\yangans-website\yangans-admin\target\classes started by YangAns in D:\yangans-website)
2025-07-30 23:20:49.406 [main] DEBUG com.yangans.admin.YangAnsApplication - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-30 23:20:49.407 [main] INFO  com.yangans.admin.YangAnsApplication - The following 1 profile is active: "dev"
2025-07-30 23:20:50.237 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-30 23:20:50.238 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Using auto-configuration base package 'com.yangans.admin'
2025-07-30 23:20:50.300 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 23:20:50.303 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 23:20:50.335 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-07-30 23:20:50.477 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleMapper.class]
2025-07-30 23:20:50.477 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\ArticleTagMapper.class]
2025-07-30 23:20:50.477 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\CategoryMapper.class]
2025-07-30 23:20:50.477 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\TagMapper.class]
2025-07-30 23:20:50.478 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\yangans-website\yangans-admin\target\classes\com\yangans\admin\mapper\UserMapper.class]
2025-07-30 23:20:50.479 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleMapper' and 'com.yangans.admin.mapper.ArticleMapper' mapperInterface
2025-07-30 23:20:50.483 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'articleTagMapper' and 'com.yangans.admin.mapper.ArticleTagMapper' mapperInterface
2025-07-30 23:20:50.483 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'categoryMapper' and 'com.yangans.admin.mapper.CategoryMapper' mapperInterface
2025-07-30 23:20:50.484 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tagMapper' and 'com.yangans.admin.mapper.TagMapper' mapperInterface
2025-07-30 23:20:50.485 [main] DEBUG org.mybatis.spring.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.yangans.admin.mapper.UserMapper' mapperInterface
2025-07-30 23:20:51.111 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-30 23:20:51.122 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 23:20:51.123 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 23:20:51.293 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-30 23:20:51.293 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1844 ms
2025-07-30 23:20:52.363 [main] DEBUG c.y.a.component.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-30 23:20:53.191 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 60 mappings in 'requestMappingHandlerMapping'
2025-07-30 23:20:53.427 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/admin/auth/login']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources/**']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/actuator/**']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for Ant [pattern='/api/admin/**']
2025-07-30 23:20:53.443 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [authenticated] for any request
2025-07-30 23:20:53.452 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@44c27b8b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@b842275, org.springframework.security.web.context.SecurityContextPersistenceFilter@4535bdc6, org.springframework.security.web.header.HeaderWriterFilter@4e030feb, org.springframework.security.web.authentication.logout.LogoutFilter@17a77a7e, com.yangans.admin.component.security.JwtAuthenticationFilter@38a96593, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6df87ffd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@608f310a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33399052, org.springframework.security.web.session.SessionManagementFilter@24c84e65, org.springframework.security.web.access.ExceptionTranslationFilter@24010875, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e2a76be]
2025-07-30 23:20:53.637 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-30 23:20:53.691 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/swagger-ui/] in 'viewControllerHandlerMapping'
2025-07-30 23:20:53.715 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui/**] in 'resourceHandlerMapping'
2025-07-30 23:20:53.725 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-30 23:20:53.746 [main] DEBUG c.b.m.autoconfigure.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-30 23:20:53.988 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-30 23:20:54.456 [main] INFO  com.yangans.admin.YangAnsApplication - Started YangAnsApplication in 5.524 seconds (JVM running for 6.492)
2025-07-30 23:21:10.173 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 23:21:10.173 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 23:21:10.173 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-30 23:21:10.173 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-30 23:21:10.173 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-30 23:21:10.175 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7c0a3f14
2025-07-30 23:21:10.175 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@8cd2442
2025-07-30 23:21:10.175 [http-nio-8080-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-30 23:21:10.175 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 23:21:10.213 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /admin/categories?_t=1753888869973
2025-07-30 23:21:10.213 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /admin/articles?current=1&size=20&orderBy=created_at&orderDirection=desc&_t=1753888869973
2025-07-30 23:21:10.223 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-30 23:21:10.223 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-30 23:21:10.231 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-30 23:21:10.231 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-30 23:21:10.248 [http-nio-8080-exec-2] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Failed to authorize filter invocation [GET /admin/categories?_t=1753888869973] with attributes [authenticated]
2025-07-30 23:21:10.248 [http-nio-8080-exec-1] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Failed to authorize filter invocation [GET /admin/articles?current=1&size=20&orderBy=created_at&orderDirection=desc&_t=1753888869973] with attributes [authenticated]
2025-07-30 23:21:10.250 [http-nio-8080-exec-2] WARN  c.y.a.c.security.JwtAuthenticationEntryPoint - 未授权访问: /api/admin/categories - Full authentication is required to access this resource
2025-07-30 23:21:10.250 [http-nio-8080-exec-1] WARN  c.y.a.c.security.JwtAuthenticationEntryPoint - 未授权访问: /api/admin/articles - Full authentication is required to access this resource
2025-07-30 23:21:10.343 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-30 23:21:10.344 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-30 23:21:33.264 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /admin/articles?current=1&size=20&orderBy=created_at&orderDirection=desc&_t=1753888893260
2025-07-30 23:21:33.264 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-30 23:21:33.264 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-30 23:21:33.264 [http-nio-8080-exec-6] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Failed to authorize filter invocation [GET /admin/articles?current=1&size=20&orderBy=created_at&orderDirection=desc&_t=1753888893260] with attributes [authenticated]
2025-07-30 23:21:33.265 [http-nio-8080-exec-6] WARN  c.y.a.c.security.JwtAuthenticationEntryPoint - 未授权访问: /api/admin/articles - Full authentication is required to access this resource
2025-07-30 23:21:33.266 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request

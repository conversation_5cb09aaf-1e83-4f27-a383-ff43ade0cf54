@echo off
echo ========================================
echo YangAns博客系统 - API连接测试脚本
echo ========================================
echo.

echo 正在测试后端API连接...
echo.

:: 测试后端服务是否启动
echo [1/4] 检查后端服务状态...
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:8080/api/actuator/health
if %errorlevel% neq 0 (
    echo [错误] 后端服务未启动或无法访问
    echo 请确保后端服务正在运行在 http://localhost:8080
    pause
    exit /b 1
) else (
    echo [成功] 后端服务正常运行
)
echo.

:: 测试CORS配置
echo [2/4] 测试CORS跨域配置...
curl -s -H "Origin: http://localhost:3002" -H "Access-Control-Request-Method: GET" -H "Access-Control-Request-Headers: Authorization" -X OPTIONS http://localhost:8080/api/admin/categories
if %errorlevel% neq 0 (
    echo [警告] CORS预检请求失败
) else (
    echo [成功] CORS配置正常
)
echo.

:: 测试分类API
echo [3/4] 测试分类API接口...
curl -s -H "Origin: http://localhost:3002" -H "Authorization: Bearer dev-mock-token" http://localhost:8080/api/admin/categories
if %errorlevel% neq 0 (
    echo [错误] 分类API请求失败
) else (
    echo [成功] 分类API响应正常
)
echo.

:: 测试文章API
echo [4/4] 测试文章API接口...
curl -s -H "Origin: http://localhost:3002" -H "Authorization: Bearer dev-mock-token" "http://localhost:8080/api/admin/articles?current=1&size=5"
if %errorlevel% neq 0 (
    echo [错误] 文章API请求失败
) else (
    echo [成功] 文章API响应正常
)
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 如果所有测试都显示[成功]，说明API连接正常。
echo 如果有[错误]或[警告]，请检查：
echo   1. 后端服务是否正常启动
echo   2. 端口是否被占用
echo   3. 防火墙设置
echo   4. CORS配置是否正确
echo.
echo 现在可以启动前端服务进行测试：
echo   cd yangans-web/admin-panel
echo   npm run dev
echo.
pause
